<template>
  <section class="channel-settings-wrapper">
    <BfDialog
      v-model="channelVisible"
      :title="channelDialogTitle"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      class="header-border channel-settings-dialog"
    >
      <el-form ref="channel" :label-width="channelManagerLabelWidth" :model="channelData" class="grid grid-cols-1 gap-3 flex-wrap channel-page-form">
        <el-form-item :label="$t('dialog.currentChannel')" class="select-channel">
          <BfInputNumberV2 v-model="channelNo" class="!w-full" :min="1" :max="maxChannelCount" @change="channelNoChange" />
        </el-form-item>
        <el-form-item ref="sendGroup" class="send-group form-item-ellipsis">
          <template #label>
            <span class="form-item-label" :title="$t('dialog.sendGroup')" style="line-height: 50px">{{ $t('dialog.sendGroup') }}</span>
          </template>
          <BfSelect v-model="channelData.sendGroup" class="!h-[50px]" :placeholder="$t('dialog.select')" filterable>
            <el-option v-for="item in sendGroupList" :key="item.key" :label="item.label" :value="item.key" />
          </BfSelect>
        </el-form-item>
        <el-form-item :label="$t('dialog.area')" class="cascader-wrap">
          <bf-cascader
            v-model="selectedChannelZone"
            :options="selectedChannelZoneList"
            filterable
            clearable
            class="h-[50px]"
            :placeholder="$t('dialog.selectZone')"
            @change="selectedChannelZoneChange"
          />
          <el-button class="cascader-btn bf-form-item-button !h-[50px] !ml-[10px]" @click="editChannelZone" v-text="$t('dialog.editZone')" />
        </el-form-item>
        <el-form-item :label="$t('dialog.copyChannel')" class="cascader-wrap">
          <bf-cascader
            v-model="willCopyDevice"
            :options="cascaderDevOpts"
            filterable
            clearable
            class="!h-[50px]"
            :placeholder="$t('dialog.readFromDevice')"
            :props="{ checkStrictly: true }"
          />
          <el-button
            class="bf-form-item-button !h-[50px] !ml-[10px]"
            :disabled="willCopyDevice.length === 0"
            @click="copyDeviceChannels"
            v-text="$t('dialog.copy')"
          />
        </el-form-item>
        <el-form-item label-width="0">
          <bf-transfer
            v-model="channelData.listenGroup"
            class="flex flex-col md:flex-row gap-2 justify-between items-center channel-manager-transfer"
            filterable
            :titles="transferTitles"
            :button-texts="buttonTexts"
            :format="transferLabelFormat"
            :data="willListenData"
            target-order="push"
            @right-check-change="rightCheckChange"
            @change="listenGroupChange"
          >
            <template #right-footer>
              <el-button class="transfer-footer" :title="$t('dialog.moveUp')" icon="top" circle :disabled="rightCheckList.length === 0" @click="moveUp" />
              <el-button class="transfer-footer" :title="$t('dialog.moveDn')" icon="bottom" circle :disabled="rightCheckList.length === 0" @click="moveDown" />
            </template>
          </bf-transfer>
        </el-form-item>
      </el-form>

      <template #footer>
        <section class="data_btns flex justify-center gap-3">
          <BfButton color-type="warning" @click="clearChannelConfig">
            {{ $t('dialog.clearChannelConfig') }}
          </BfButton>
          <BfButton color-type="warning" @click="updateDeviceChannel">
            {{ $t('dialog.update') }}
          </BfButton>
          <BfButton color-type="default" @click="openBatchSettingChDlg">
            {{ $t('dialog.batchSetting') }}
          </BfButton>
        </section>
      </template>
    </BfDialog>

    <!--批量设置终端信道对话框-->
    <BfDialog
      v-model="batchSettingChDlg"
      :title="$t('dialog.batchSettingChannel')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      class="header-border flex flex-col batch-setting-channel-dialog"
    >
      <el-progress
        class="flex-none batch-setting-progress"
        :percentage="batchSettingPercentage"
        :text-inside="true"
        :stroke-width="2"
        :show-text="false"
        :status="batchSettingStatus"
        :style="{
          visibility: showBatchSettingProgress ? 'visible' : 'hidden',
        }"
      />

      <div class="left flex-none copy-source-device">
        <span v-text="$t('dialog.sourceDevice') + ': '" />
        <span v-text="deviceName" />
      </div>

      <TableTree :ref="batchTreeId" :treeId="batchTreeId" :option="batchTreeOption" @loaded="initBatchSettingTree" />

      <template #footer>
        <div class="flex justify-center batch-setting-dialog-footer">
          <BfButton color-type="warning" :disabled="showBatchSettingProgress || batchTreeTarget.length === 0" @click="confirmBatchSetting">
            {{ $t('dialog.startCopy') }}
          </BfButton>
        </div>
      </template>
    </BfDialog>

    <!--编辑区域数据对话框-->
    <BfDialog
      v-model="channelZoneVisible"
      :title="$t('dialog.editZone')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      class="header-border channel-zone-dialog"
    >
      <div class="flex no-wrap h-full w-full channel-zone-container">
        <TableTree :ref="zoneTreeId" :treeId="zoneTreeId" :selected="zoneSelectedKeys" :option="zoneTreeOption" @loaded="treeLoaded" />

        <el-form v-if="showZoneEditor" ref="channelZoneForm" class="channel-zone-form" label-position="top" :model="oneChannelZone">
          <el-form-item v-if="isZoneConfigNode" :label="$t('writeFreq.terminalType')">
            <BfSelect v-model="channelZoneModel" class="!h-[50px]" disabled :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
              <el-option v-for="(item, index) in zoneConfigModelList" :key="index" :label="item.label" :value="item.value" />
            </BfSelect>
          </el-form-item>
          <el-form-item v-else :label="$t('dialog.areaNumber')">
            <BfInputNumberV2 v-model="oneChannelZone.zoneNo" class="!w-full" :min="1" :max="maxZoneNo" />
          </el-form-item>
          <el-form-item :label="areaNameLabel">
            <BfInput v-model="oneChannelZone.zoneTitle" :maxlength="16" style="height: 50px" />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="center flex justify-center gap-3">
          <BfButton color-type="default" @click="editChannelZoneConfig">
            {{ $t('writeFreq.zoneConfig') }}
          </BfButton>
          <BfButton color-type="danger" :disabled="!selectZoneNode" @click="delChannelZone">
            {{ $t('dialog.delete') }}
          </BfButton>
          <BfButton color-type="warning" :disabled="!selectZoneNode" @click="updateChannelZone">
            {{ $t('dialog.update') }}
          </BfButton>
          <BfButton color-type="default" :disabled="disAddZone" @click="addChannelZone">
            {{ $t('dialog.add') }}
          </BfButton>
        </div>
      </template>
    </BfDialog>

    <!--编辑区域配置对话框-->
    <BfDialog
      v-model="zoneConfigVisible"
      :title="$t('writeFreq.zoneConfig')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      append-to-body
      class="header-border zone-config-dialog"
    >
      <el-form
        ref="zoneConfigForm"
        class="grid grid-cols-1 zone-config-form"
        :label-width="newZoneConfigLabelWidth"
        :model="oneZoneConfig"
        :rules="oneZoneConfigRules"
      >
        <el-form-item :label="$t('writeFreq.configName')" prop="title">
          <BfInput v-model="oneZoneConfig.title" autofocus :maxlength="16" style="height: 50px" />
        </el-form-item>
        <el-form-item :label="$t('writeFreq.terminalType')" prop="model">
          <BfSelect v-model="oneZoneConfig.model" class="!h-[50px]" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="(item, index) in zoneConfigModelList" :key="index" :label="item.label" :value="item.value" />
          </BfSelect>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="center flex justify-center gap-3">
          <BfButton color-type="primary" @click="confirmOneZoneConfig">
            {{ $t('dialog.add') }}
          </BfButton>
        </div>
      </template>
    </BfDialog>
  </section>
</template>

<script>
  import { SupportedLang } from '@/modules/i18n'
  import TableTree from '@/components/common/tableTree'
  import bfutil, { DeviceTypes, getCommonOrgType, notSupportedChannelTypes } from '@/utils/bfutil'
  import { getDeviceModelList, getDeviceModelName } from '@/writingFrequency/customModelConfig'
  import * as deviceModels from '@/writingFrequency/interphone/models'
  import bfproto from '@/modules/protocol'
  import bfTime from '@/utils/time'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfNotify from '@/utils/notify'
  import { defaultTreeId, getOrgNodeTitle } from '@/utils/bftree'
  import { cloneDeep } from 'lodash'
  import { getModelName } from '@/writingFrequency/modelInfo'
  import bfprocess from '@/utils/bfprocess'
  import { v1 as uuid } from 'uuid'
  import validateRules from '@/utils/validateRules'
  import BfDialog from '@/components/bfDialog/main'
  import BfButton from '@/components/bfButton/main'
  import BfInputNumberV2 from '@/components/bfInputNumber/main'
  import BfSelect from '@/components/bfSelect/main'
  import BfCascader from '@/components/bfCascader/main'
  import BfInput from '@/components/bfInput/main'
  import BfTransfer from '@/components/bfTransfer/main'

  const commonOrgType = getCommonOrgType()
  const filterDynamicGroup = dataList => {
    return dataList.filter(data => commonOrgType.includes(data.orgIsVirtual))
  }

  const customModelList = getDeviceModelList()
  const zoneModels =
    customModelList.length > 0
      ? customModelList
      : [
          deviceModels.TD510SDCModel,
          deviceModels.TD510SVTModel,
          deviceModels.TD511SDCModel,
          deviceModels.TD511FRModel,
          deviceModels.TD511SVTModel,
          deviceModels.TD818SDCModel,
          deviceModels.TD818SVTModel,
          deviceModels.TM825SDCModel,
          deviceModels.TM825FRModel,
          deviceModels.TM8250SDCR7F,
          deviceModels.TD910SDCModel,
          deviceModels.TD910PSDCModel,
          deviceModels.TD920Model,
          deviceModels.TD930SDCModel,
          deviceModels.TD930SDCR7F,
          deviceModels.BP750SDC,
          deviceModels.TD930SVTModel,
          deviceModels.TD930SVTR7F,
          deviceModels.BP750SVT,
          deviceModels.TD880SDCModel,
          deviceModels.TD880SVTModel,
          deviceModels.BP610Model,
          deviceModels.BP620Model,
          deviceModels.BP660Model,
          deviceModels.BP860SDCModel,
          deviceModels.BP860SVTModel,
          deviceModels.TM8250SVTR7F,
        ]
  const modelZoneLevelLimit = {
    [deviceModels.TD910SDCModel]: {
      1: 4,
      2: 1000,
      3: 2000,
    },
    [deviceModels.TD910PSDCModel]: {
      1: 4,
      2: 1000,
      3: 2000,
    },
    [deviceModels.TD920Model]: {
      1: 4,
      2: 1000,
      3: 2000,
    },
    [deviceModels.TM8250SDCR7F]: {
      1: 128,
    },
    [deviceModels.TD930SDCR7F]: {
      1: 128,
    },
    [deviceModels.TD930SVTR7F]: {
      1: 128,
    },
    [deviceModels.BP750SDC]: {
      1: 128,
    },
    [deviceModels.BP750SVT]: {
      1: 128,
    },
    [deviceModels.BP610Model]: {
      1: 64,
    },
    [deviceModels.BP620Model]: {
      1: 64,
    },
    [deviceModels.BP660Model]: {
      1: 64,
    },
    [deviceModels.BP860SDCModel]: {
      1: 16,
    },
    [deviceModels.BP860SVTModel]: {
      1: 16,
    },
    [deviceModels.TM8250SVTR7F]: {
      1: 128,
    },
    // TD081100, 510SDC00, 511SDC00, 511FR000, 825SDC00, 8250FR00, 930SDC00, 930SVT00
    default: {
      1: 32,
    },
  }
  const currentZoneModel = deviceModels.TD511SDCModel

  function getModelZoneLevelLimit(model) {
    return modelZoneLevelLimit[model] ?? modelZoneLevelLimit.default
  }

  function isExcludeType(type) {
    return notSupportedChannelTypes.includes(type)
  }

  const DefaultChannelData = {
    // 当前信道收听组
    listenGroup: [],
    // 当前信道发射组
    sendGroup: '',
    // 当前设备信道编号
    no: 1,
    zoneRid: bfutil.DefOrgRid,
  }
  const dbSubject = `db.${bfglob.sysId}`

  export default {
    name: 'ChannelSettings',
    emits: ['update:device', 'update:visible'],
    components: {
      TableTree,
      BfDialog,
      BfButton,
      BfInputNumberV2,
      BfSelect,
      BfCascader,
      BfInput,
      BfTransfer,
    },
    props: {
      visible: {
        type: Boolean,
        required: true,
      },
      device: {
        type: Object,
        required: true,
      },
    },
    data() {
      const Devices = bfutil.objToArray(bfglob.gdevices.getAll())

      return {
        gdevices: bfglob.gdevices.getAll(),
        targetDeviceRid: '',
        globalDeviceList: bfglob.gdevices.getList(),

        // 当前设备所有信道设置,[{no:1,sendGroup:"",listenGroup:[]}]
        channelsMap: new Map(),
        channelData: {
          ...DefaultChannelData,
          sendGroup: bfglob.gdevices.getParent(this.device.orgId)?.dmrId ?? '',
        },
        channelNo: DefaultChannelData.no,
        // 区域数据，多级级联选择
        selectedChannelZone: [],
        // 当前信道监听组
        listenGroupLimit: 16,
        // 从其他设置读取信道数据路径选中值数组
        willCopyDevice: [],
        // 穿梭框右侧列表元素被用户选中的值
        rightCheckList: [],
        // 穿梭框数据源
        willListenData: [],

        // 批量设置终端信道参数
        batchTreeId: 'batch-setting-tree',
        batchSettingChDlg: false,
        showBatchSettingProgress: false,
        batchSettingPercentage: 0,
        batchSettingFailed: false,
        batchTreeTarget: [],
        batchSettingNo: 0,

        // 信道区域编辑
        devices: Devices,
        channelZoneVisible: false,
        oneChannelZone: {
          rid: bfutil.DefOrgRid,
          zoneParent: bfutil.DefOrgRid,
          zoneNo: 0,
          zoneTitle: 'root',
        },
        maxChannelCount: 3773,
        channelZones: bfutil.objToArray(bfglob.gchannelZone.getAll()),
        channelZoneList: bfglob.gchannelZone.getList(),
        selectZoneNode: undefined,
        maxZoneLevel: 1,
        zoneConfigVisible: false,
        oneZoneConfig: {
          title: '',
          model: zoneModels.includes(currentZoneModel) ? currentZoneModel : (zoneModels[0] ?? ''),
        },
        zoneTreeId: 'zoneTree',
      }
    },
    methods: {
      clickNode(event, data) {
        const excludeList = ['expander', 'prefix', 'checkbox']
        if (excludeList.includes(data.targetType)) {
          return true
        }
        const node = data.node
        if (!node) {
          return false
        }
        node.setActive()
        node.setSelected(!node.isSelected())
      },

      /* 批量设置信道参数方法 */
      // 初始化批量设置终端列表树
      initBatchSettingTree() {
        // 不需要复制虚拟单位，网关、坐席等节点
        this.batchSetTree
          .toDictTree(defaultTreeId, dict => {
            const extraData = dict.data || {}
            // 根节点
            if (dict.key.startsWith('root')) return dict

            // 单位节点，过滤动态组和虚拟单位
            if (dict.folder) {
              if (extraData.orgIsVirtual >= 100 || extraData.virtual) return false
              // 批量复制时，树形默认全部折叠
              dict.expanded = false
              // 重写单位节点标题
              const orgData = bfglob.gorgData.get(dict.key)
              if (orgData) {
                dict.title = getOrgNodeTitle(orgData, { showCounter: false })
              }
            } else {
              // 过滤不支持信道配置的终端
              if (!dict.folder && isExcludeType(extraData.deviceType)) return false
            }

            dict.select = false

            return dict
          })
          .then(() => {
            this.batchSetTree.sortChildren()
            this.batchSetTree.updateViewport(this.batchSetTree.getLocalTree())
          })
      },
      batchSetTreeOnSelect(event, data) {
        const selectedNodes = data.tree.getSelectedNodes()
        this.processBatchTreeSelectedNode(selectedNodes)
      },
      processBatchTreeSelectedNode(selectedNodes) {
        const res = []
        const resCache = new Set()
        for (let i = 0; i < selectedNodes.length; i++) {
          const node = selectedNodes[i]
          // 跳过单位节点
          if (node.isFolder()) {
            continue
          }
          // 跳过本地不存在的节点
          const device = bfglob.gdevices.get(node.key.slice(0, 36))
          if (!device) {
            continue
          }
          if (resCache.has(device.rid)) {
            continue
          }

          res.push(device)
          resCache.add(device.rid)
        }
        this.batchTreeTarget = res
      },
      confirmBatchSetting() {
        // 生成迭代器
        const iterator = this.batchTreeTarget[Symbol.iterator]()
        // 初始化进度条进度值
        this.batchSettingNo = 0
        // 使用协议结构生成新对象，避免对象引用时多出其他无效字段保存到数据库中
        const channels = this.getChannelsFromMap()
        const updateChannelConfig = async iteratorItem => {
          if (iteratorItem.done) {
            // 所有的信道都已经更新完成，则结束进度条
            this.progressSucess()
            // 向用户提示设置成功
            bfNotify.messageBox(this.$t('msgbox.batchSettingChannelSuccess'), 'success')
            return
          }

          const isOk = await this.updateOneDeviceChannel(iteratorItem.value, channels)
          if (isOk) {
            // 计算进度
            this.progressLoading()
            // 继续更新下个目标
            updateChannelConfig(iterator.next())
          } else {
            // 恢复进度条默认参数
            this.progressFailed()
            bfNotify.warningBox(this.$t('msgbox.batchSettingChannelFailed'), 'error')
          }
        }

        updateChannelConfig(iterator.next())
      },
      progressLoading() {
        if (this.batchSettingFailed) {
          this.batchSettingFailed = false
        }
        if (!this.showBatchSettingProgress) {
          this.showBatchSettingProgress = true
        }

        const val = parseInt((++this.batchSettingNo / this.batchTargetTotalLen) * 100)
        this.batchSettingPercentage = val > 100 ? 100 : val
      },
      progressSucess(animate = true) {
        const endProgress = () => {
          this.showBatchSettingProgress = false
          this.batchSettingFailed = true
          this.batchSettingPercentage = 0
        }
        if (animate) {
          setTimeout(() => {
            endProgress()
          }, 600)
        } else {
          endProgress()
        }
      },
      progressFailed(animate = true) {
        this.batchSettingFailed = true

        const endProgress = () => {
          this.showBatchSettingProgress = false
          this.batchSettingPercentage = 0
        }

        if (animate) {
          setTimeout(() => {
            endProgress()
          }, 600)
        } else {
          endProgress()
        }
      },
      // 打开批量复制信道窗口
      openBatchSettingChDlg() {
        this.batchSettingChDlg = true
        this.$nextTick(() => {
          // 每次打开都清空节点选择
          if (this.batchTreeTarget.length > 0) {
            this.batchSetTree.selectAll(false)
            this.batchTreeTarget = []
          }
        })
      },

      // 打开区域编辑窗口
      editChannelZone() {
        this.channelZoneVisible = true
      },
      zoneTreeSelect(event, data) {
        const nodes = data.tree.getSelectedNodes(true)
        if (!nodes || nodes.length === 0) {
          this.selectZoneNode = undefined
          return
        }
        const node = (this.selectZoneNode = nodes[0])
        const zone = bfglob.gchannelZone.get(node.key)
        if (!zone) {
          return
        }
        this.oneChannelZone = cloneDeep(zone)
      },
      getZoneTitle(data) {
        const isRootNode = data.zoneParent === bfutil.DefOrgRid
        const zoneTitleLabel = `<span>${data.zoneTitle}</span>`
        if (isRootNode) {
          return zoneTitleLabel
        }
        const zoneNoLabel = `<span class='device_channel zone-no'>${data.zoneNo}</span>`
        return `${zoneNoLabel}${zoneTitleLabel}`
      },
      createZoneNode(data) {
        const isRootNode = data.zoneParent === bfutil.DefOrgRid
        return {
          key: data.rid,
          title: this.getZoneTitle(data),
          icon: isRootNode ? 'iconfont icon-simple-list' : false,
          expanded: false,
          zoneNo: data.zoneNo,
          children: [],
          origin: data,
          sortString: data.zoneNo,
        }
      },
      loadZoneTreeNode() {
        if (!this.tableTree) return

        const zoneMap = {}
        const children = []
        for (let i = 0; i < this.channelZones.length; i++) {
          const zone = this.channelZones[i]
          // 有可能先生成子节点同时生成了父节点，需要过滤可能的重复节点
          if (zoneMap[zone.rid]) {
            continue
          }

          const zoneParent = zone.zoneParent
          // 顶级配置节点，直接放到zoneMap中
          if (zoneParent === bfutil.DefOrgRid) {
            zoneMap[zone.rid] = this.createZoneNode(zone)
            children.push(zoneMap[zone.rid])
            continue
          }
          // 处理配置节点下所有区域节点数据
          // 如果还没有生成父节点数据，则先生成父节点
          if (!zoneMap[zoneParent]) {
            const parentZone = bfglob.gchannelZone.get(zoneParent)
            if (!parentZone) {
              continue
            }
            zoneMap[zoneParent] = this.createZoneNode(parentZone)
          }
          const node = this.createZoneNode(zone)
          zoneMap[zoneParent].children.push(node)
          zoneMap[zone.rid] = node
        }

        if (children.length) {
          const rootNode = this.tableTree.getRootNode()
          this.tableTree.addNodeChildren(rootNode, children)
        }
        this.tableTree.sortChildren()
      },
      treeLoaded() {
        this.loadZoneTreeNode()
      },
      getHasSettingZone(zone) {
        let zoneCache = cloneDeep(zone)
        while (zoneCache) {
          if (zoneCache.zoneParent === bfutil.DefOrgRid) {
            try {
              zoneCache.setting = JSON.parse(zoneCache.setting)
              return zoneCache
            } catch (_e) {
              return undefined
            }
          } else {
            zoneCache = cloneDeep(bfglob.gchannelZone.get(zoneCache.zoneParent))
          }
        }
        return undefined
      },
      editChannelZoneConfig() {
        this.zoneConfigVisible = true
      },
      delChannelZone() {
        const msgObj = {
          ...this.oneChannelZone,
        }

        bfproto
          .sendMessage(dbCmd.DB_DEVICE_CHANNEL_ZONE_DELETE, msgObj, 'db_device_channel_zone', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('delChannelZone res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.gchannelZone.delete(msgObj.rid)
              this.channelZones = bfutil.objToArray(bfglob.gchannelZone.getAll())
              this.tableTree?.removeNode(msgObj.rid)

              // 添加查询日志
              const note = this.$t('dialog.delete') + msgObj.zoneNo + ' / ' + msgObj.zoneTitle + this.$t('dialog.areaData')
              bfglob.emit('addnote', note)
            } else {
              bfglob.console.log('delChannelZone failed:', rpc_cmd_obj, msgObj)
              bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            }
          })
          .catch(err => {
            bfglob.console.warn('delChannelZone failed:', err)
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
          })
      },
      updateChannelZone() {
        const msgObj = {
          ...this.oneChannelZone,
          zoneLevel: this.getZoneLevel(this.oneChannelZone.zoneParent),
          setting: this.oneChannelZone.setting || JSON.stringify({}),
        }

        bfproto
          .sendMessage(dbCmd.DB_DEVICE_CHANNEL_ZONE_UPDATE, msgObj, 'db_device_channel_zone', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('updateChannelZone res:', rpc_cmd_obj, msgObj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')
              bfprocess.setDeviceChannelZone(msgObj)
              this.channelZones = bfutil.objToArray(bfglob.gchannelZone.getAll())
              this.updateZoneTitle(msgObj)

              // 添加查询日志
              const note = this.$t('dialog.update') + msgObj.zoneNo + ' / ' + msgObj.zoneTitle + this.$t('dialog.areaData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('idx_db_device_channel_zone')) {
                bfNotify.messageBox(this.$t('writeFreq.areaNoCannotRepeated'), 'warning')
                return
              }
              bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            }
          })
          .catch(err => {
            bfglob.console.warn('updateChannelZone failed:', err)
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
          })
      },
      getZoneLevel(parentRid) {
        const zoneParent = bfglob.gchannelZone.get(parentRid)
        if (!zoneParent) {
          return 1
        }
        return zoneParent.zoneLevel + 1
      },
      updateZoneTitle(data) {
        this.tableTree?.renderTitle(data.rid, this.getZoneTitle(data))
      },
      addZoneToDataBase(data = this.oneChannelZone, selectNode = this.selectZoneNode) {
        return new Promise((resolve, reject) => {
          const zoneParent = selectNode.key || bfutil.DefOrgRid
          const isRootNode = data.zoneParent === bfutil.DefOrgRid
          const msgObj = {
            ...data,
            zoneLevel: this.getZoneLevel(zoneParent),
            rid: uuid(),
            zoneParent: zoneParent,
            zoneNo: this.getNextZoneNo(zoneParent),
            setting: JSON.stringify(isRootNode ? {} : data.setting || {}),
          }

          bfproto
            .sendMessage(dbCmd.DB_DEVICE_CHANNEL_ZONE_INSERT, msgObj, 'db_device_channel_zone', dbSubject)
            .then(rpc_cmd_obj => {
              bfglob.console.log('addChannelZone res:', rpc_cmd_obj, msgObj)
              if (rpc_cmd_obj.resInfo === '+OK') {
                bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
                bfprocess.setDeviceChannelZone(msgObj)
                const tree = this.$refs[this.zoneTreeId].$el
                this.addZoneNode(tree, msgObj)
                this.channelZones = bfutil.objToArray(bfglob.gchannelZone.getAll())

                // 添加查询日志
                const note = this.$t('dialog.add') + msgObj.zoneNo + ' / ' + msgObj.zoneTitle + this.$t('dialog.areaData')
                bfglob.emit('addnote', note)

                resolve(msgObj)
              } else {
                reject(rpc_cmd_obj.resInfo)
                if (rpc_cmd_obj.resInfo.includes('idx_db_device_channel_zone')) {
                  bfNotify.messageBox(this.$t('writeFreq.areaNoCannotRepeated'), 'warning')
                  return
                }
                bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
              }
            })
            .catch(err => {
              reject()
              bfglob.console.warn('addChannelZone failed:', err)
              bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            })
        })
      },
      addChannelZone() {
        this.addZoneToDataBase()
      },
      getNextNo(idList, min = 1, limit = 0xff) {
        if (!Array.isArray(idList)) {
          return min
        }

        let id = min
        while (id < limit) {
          if (!idList.includes(id)) {
            return id
          }
          id++
        }
        return id
      },
      getNextZoneNo(zoneParent) {
        if (!zoneParent) {
          return 1
        }

        const zoneNoList = []
        for (let i = 0; i < this.channelZones.length; i++) {
          const item = this.channelZones[i]
          if (item.zoneParent === zoneParent) {
            zoneNoList.push(item.zoneNo)
          }
        }

        return this.getNextNo(zoneNoList)
      },
      addZoneNode(treeId, data, rootNode) {
        if (!this.tableTree) return
        // 判断上级节点是否已经加载，未加载则先加载完上级节点
        if (!rootNode) {
          rootNode = this.tableTree.getRootNode()
        }
        const key = data.zoneParent
        let parentNode
        if (!key || key === bfutil.DefOrgRid) {
          parentNode = rootNode
        } else {
          parentNode = this.tableTree.getNodeByKey(key)
        }

        if (!parentNode) {
          const parentData = bfglob.gchannelZone.get(key)
          if (typeof parentData === 'undefined') {
            parentNode = rootNode
          } else {
            parentNode = this.addZoneNode(treeId, parentData, rootNode)
          }
        }
        return this.tableTree.addNodeChildren(parentNode, this.createZoneNode(data))
      },
      confirmOneZoneConfig() {
        this.$refs.zoneConfigForm.validate().then(() => {
          // 配置当成一个区域数据添加到数据库
          const config = {
            zoneTitle: this.oneZoneConfig.title,
            setting: {
              model: this.oneZoneConfig.model,
            },
          }
          const nodeConfig = {
            key: bfutil.DefOrgRid,
          }
          const addToDataBase = (data, option) => {
            this.addZoneToDataBase(data, option)
              .then(_res => {
                this.zoneConfigVisible = false
                this.oneZoneConfig.title = ''
              })
              .catch(resInfo => {
                // 序号重复，递增重试
                if (typeof resInfo === 'string' && resInfo.includes('idx_db_device_channel_zone')) {
                  data.zoneNo++ < 32 && addToDataBase(data, option)
                }
              })
          }
          addToDataBase(config, nodeConfig)
        })
      },

      // 更新一个终端的信道配置
      updateOneDeviceChannel(device, channels) {
        if (!device) {
          return Promise.resolve(false)
        }

        const msgObj = {
          orgId: device.orgId,
          rid: device.rid,
          channel: JSON.stringify({ channels: channels }),
          channelLastModifyTime: bfTime.nowUtcTime(),
          lastRfConfigTime: device.lastRfConfigTime || bfTime.nowUtcTime(),
          dmrId: device.dmrId,
        }
        const msgOpts = {
          rpcCmdFields: {
            origReqId: 'rid',
            resInfo: 'org_id,channel,channel_last_modify_time,last_rf_config_time',
          },
        }

        // 更新数据库信道数据
        return bfproto
          .sendMessage(dbCmd.DB_DEVICE_PUPDATE, msgObj, 'db_device', dbSubject, msgOpts)
          .then(rpc_cmd_obj => {
            const isOk = rpc_cmd_obj.resInfo === '+OK'
            if (isOk) {
              // 成功更新设备信道，同步本地数据
              bfglob.emit('pupdate_global_deviceData', msgObj, msgOpts.rpcCmdFields)
            } else {
              this.showUpdateErrorMsg(rpc_cmd_obj.resInfo)
            }

            return Promise.resolve(isOk)
          })
          .catch(err => {
            bfglob.console.error('updateOneDeviceChannel catch:', err, device, channels)
            return Promise.resolve(false)
          })
      },
      // 从缓存的Map中读取信道配置数组
      getChannelsFromMap() {
        const channels = []
        for (const [, channel] of this.channelsMap) {
          // 使用协议结构生成新对象，避免对象引用时多出其他无效字段保存到数据库中
          const oneChannelItemCls = bfproto.bfdx_proto_msg_T('OneChannelItem', 'bfkcp')
          // 清除默认的uuid，信道配置数据是保存在终端的setting中，不需要保存
          if (channel.zoneRid === bfutil.DefOrgRid) {
            channel.zoneRid = ''
          }
          const oneChannelItem = oneChannelItemCls.create(channel)
          channels.push(oneChannelItem)
        }

        return bfutil.sortChannels(channels)
      },
      // 判断信道的配置是否有效，有效则返回true，反之false
      checkChannelDataIsValid(channel) {
        return channel.listenGroup.length !== 0
      },
      // 选择的区域变更，更新信道配置的参数
      selectedChannelZoneChange(zoneRids) {
        if (zoneRids.length > 1) {
          this.channelData.zoneRid = zoneRids[zoneRids.length - 1]
        } else {
          this.channelData.zoneRid = bfutil.DefOrgRid
        }
      },
      // 切换信道
      channelNoChange(chNo) {
        // 切换新的信道上
        this.channelNo = chNo
        const channel = this.channelsMap.get(chNo)
        // 开启新的信道配置
        if (!channel) {
          this.channelData = {
            ...DefaultChannelData,
            no: chNo,
            // 设置默认发射组
            sendGroup: this.getDefSendGroupDmrId(),
          }
          this.selectedChannelZone = []
          return
        }

        // 显示对应的信道的数据
        this.channelData = Object.assign({}, this.channelData, channel)
        // 设置区域信息
        this.selectedChannelZone = this.getAllParentRids(channel.zoneRid)
      },
      // 保存设备信道设置，更新设备的channel属性
      async updateDeviceChannel() {
        const channels = this.getChannelsFromMap()
        const isOk = await this.updateOneDeviceChannel(this.device, channels)
        // 成功后，需要同步父组件数据
        if (isOk) {
          bfNotify.messageBox(this.$t('msgbox.updateDeviceChannelSuccess'), 'success')
          const data = {
            ...cloneDeep(this.device),
            channels,
            channel: JSON.stringify({ channels: channels }),
            channelLastModifyTime: bfTime.nowUtcTime(),
            lastRfConfigTime: this.device.lastRfConfigTime || bfTime.nowUtcTime(),
          }
          this.updateDevice(data)
        }
      },
      // 清除当前设备的所有信道设置，需要点击更新按钮更新到数据库
      clearChannelConfig() {
        this.channelsMap.clear()
        this.channelNo = 1
        this.channelData = {
          no: 1,
          sendGroup: this.getDefSendGroupDmrId(),
          listenGroup: this.getDefaultListenGroup(),
          zoneRid: bfutil.DefOrgRid,
        }
        this.channelsMap.set(this.channelData.no, this.channelData)
      },
      // 数组两个元素交换位置方法
      swap(i, j) {
        this.channelData.listenGroup.splice(i, 0, this.channelData.listenGroup.splice(j, 1)[0])
      },
      // 收听组选中列表按收听组的位置顺序排序
      sortRightCheckList() {
        this.rightCheckList.sort((a, b) => {
          return this.channelData.listenGroup.indexOf(a) > this.channelData.listenGroup.indexOf(b) ? 1 : -1
        })
      },
      moveUp() {
        // 先对选取的元素按收听组中的位置顺序排序
        this.sortRightCheckList()

        // 取选中的元素集中第一个,找到在收听组中的位置
        const item = this.rightCheckList[0]
        const curr_idx = this.channelData.listenGroup.indexOf(item)
        // 判断其在收听组是否为第一个，是则停止上移
        if (curr_idx === 0) {
          return
        }
        // 交换两个元素位置
        this.swap(curr_idx, curr_idx - 1)

        // 否则选中元素集中余下的全部向上移动一个位置
        for (let i = 1; i < this.rightCheckList.length; i++) {
          const item = this.rightCheckList[i]
          const curr_idx = this.channelData.listenGroup.indexOf(item)
          this.swap(curr_idx, curr_idx - 1)
        }
      },
      moveDown() {
        // 先对选取的元素按收听组中的位置顺序排序
        this.sortRightCheckList()

        // 取选中的元素集中最后一个,找到在收听组中的位置
        const length = this.channelData.listenGroup.length
        const len = this.rightCheckList.length
        const item = this.rightCheckList[len - 1]
        const curr_idx = this.channelData.listenGroup.indexOf(item)
        // 判断其在收听组是否为最后一个，是则停止上移
        if (curr_idx === length - 1) {
          return
        }
        // 交换两个元素位置
        this.swap(curr_idx, curr_idx + 1)

        // 否则选中元素集中余下的全部向下移动一个位置,len-2排除掉最后一个
        for (let i = len - 2; i >= 0; i--) {
          const item = this.rightCheckList[i]
          const curr_idx = this.channelData.listenGroup.indexOf(item)
          this.swap(curr_idx, curr_idx + 1)
        }
      },
      rightCheckChange(val) {
        this.rightCheckList = Object.assign([], val)
      },
      listenGroupChange(listenGroup, direction) {
        // 向右移动时，判断接收组元素上限
        if (direction === 'right' && listenGroup.length > this.listenGroupLimit) {
          bfNotify.messageBox(
            this.$t('msgbox.setupListenGroupLimit', {
              limit: this.listenGroupLimit,
            }),
            'warning'
          )
          this.channelData.listenGroup = listenGroup.slice(0, this.listenGroupLimit)
        }
      },
      // 从其他设备读取信道设置
      copyDeviceChannels() {
        // 没有选择其他设备的信道数据
        if (this.willCopyDevice.length === 0) return

        const key = this.willCopyDevice[0]
        const device = bfglob.gdevices.get(key)
        const channel = device?.channel || '{"channels": []}'
        let channels = []
        try {
          channels = JSON.parse(channel).channels ?? []
        } catch (_e) {
          // no-empty
        }

        // 将其他设备中，与当前信道号相同的数据拷贝到当前的参数中
        const copyListenGroup = channelNo => {
          for (let i = 0; i < channels.length; i++) {
            const item = channels[i]
            if (item.no === channelNo) {
              // 过滤无效的收听组
              const listenGroup = (item.listenGroup || []).filter(item => {
                return this.sendGroupIsValid(item)
              })
              this.channelData.listenGroup = listenGroup.length ? listenGroup : this.channelData.listenGroup

              // 过滤无效的发射组
              const isValid = this.sendGroupIsValid(item.sendGroup)
              this.channelData.sendGroup = isValid ? item.sendGroup : this.channelData.sendGroup

              // 同步复制区域ID
              const zoneIds = this.getAllParentRids(item.zoneRid)
              this.selectedChannelZone = zoneIds.length ? zoneIds : this.selectedChannelZone
              return true
            }
          }
          return false
        }

        // 选中第一级，复制选中设备所有信道配置
        if (this.willCopyDevice.length === 1) {
          // 设置当前信道收听组数据
          copyListenGroup(this.channelNo)
          return
        }

        // 选中第二级，复制选中设备单个信道配置
        copyListenGroup(this.willCopyDevice[1])
      },
      // 主要用于更新终端的信道配置
      updateDevice(data) {
        this.$emit('update:device', data)
      },
      // 初始化收听组穿梭框数据
      initWillListenData() {
        const list = []
        // 需要过滤掉动态组的数据
        const orgs = filterDynamicGroup(bfutil.objToArray(bfglob.gorgData.getAll()))

        for (const k in orgs) {
          const org = orgs[k]
          list.push({
            key: org.dmrId,
            label: `${org.orgShortName}-${org.dmrId}`,
          })
        }
        this.willListenData = list
      },
      getDefaultListenGroup() {
        const listenGroup = []
        // 如果是虚拟集群对讲机，则默认添加归属组
        if (this.device.deviceType === DeviceTypes.VirtualClusterDevice) {
          listenGroup.push(this.device.devGroup)
        }
        return listenGroup
      },
      getDefSendGroupDmrId(orgId = this.device.orgId) {
        const org = bfglob.gdevices.getParent(orgId)
        // 如果查找不到终端的上级DMRID，则返回发射组列表第二个选项，第一个为全呼
        return org && org.dmrId ? org.dmrId : this.sendGroupList[1]
      },
      getAllParentRids(rid) {
        const result = []
        let zone = bfglob.gchannelZone.get(rid)
        while (!!zone && zone.rid !== bfutil.DefOrgRid) {
          result.unshift(zone.rid)
          zone = bfglob.gchannelZone.get(zone.zoneParent)
        }

        return result
      },
      sendGroupIsValid(dmrId) {
        return dmrId === bfglob.fullCallDmrId || !!bfglob.gorgData.getDataByIndex(dmrId)
      },
      initFirstChannel(channel) {
        Object.assign(this.channelData, channel)
        this.channelNo = channel.no

        // 重置收听组数据，需要过滤无效的数据，单位可能已经被删除
        this.channelData.listenGroup = (channel.listenGroup || []).filter(item => {
          return this.sendGroupIsValid(item)
        })
        const isValid = this.sendGroupIsValid(channel.sendGroup)
        this.channelData.sendGroup = isValid ? channel.sendGroup : this.getDefSendGroupDmrId()

        // 设置区域信息
        channel.zoneRid = channel.zoneRid || bfutil.DefOrgRid
        this.selectedChannelZone = this.getAllParentRids(channel.zoneRid)
      },
      // 进入信道管理界面，初始化时读取1信道数据
      initChannelData() {
        if (!this.device) {
          bfglob.console.warn('initChannelData not found device')
          return
        }

        let channels = []
        try {
          const channelJson = JSON.parse(this.device.channel)
          channels = channelJson?.channels ?? []
        } catch (_e) {
          bfglob.console.warn('initChannelData JSON.parse falid', this.device.channel)
        }

        // 将信道配置数组转换成Map,方法配置操作
        for (let i = 0; i < channels.length; i++) {
          const ch = channels[i]
          this.channelsMap.set(ch.no, ch)
        }

        // 初始化第一个信道数据到当前页面
        const firstChannel = channels[0]
        if (!firstChannel) return

        this.initFirstChannel(firstChannel)
      },
      initChannel() {
        this.initWillListenData()
        this.initChannelData()
      },
    },
    computed: {
      oneZoneConfigRules() {
        return {
          title: [
            validateRules.required(),
            {
              validator: (rule, value, callback) => {
                for (let i = 0; i < this.channelZones.length; i++) {
                  const item = this.channelZones[i]
                  if (item.zoneTitle === value) {
                    callback(new Error(this.$t('msgbox.duplicateName')))
                    return
                  }
                }
                callback()
              },
              trigger: 'blur',
            },
          ],
          model: [validateRules.required()],
        }
      },
      disAddZone() {
        if (!this.selectZoneNode) {
          return true
        }

        // 读取选中节点的顶级区域，拥有配置参数的区域
        const zone = bfglob.gchannelZone.get(this.selectZoneNode.key)
        const settingZone = this.getHasSettingZone(zone)
        if (!settingZone) {
          return false
        }

        // 通过顶级节点的配置，拿到对应机型的各区域级别最大数量
        const setting = settingZone.setting
        const levelOption = getModelZoneLevelLimit(setting.model)
        // 找不到该区域下级区域的最大数量配置，则禁止添加下级区域
        const levelLimit = levelOption[zone.zoneLevel]
        if (typeof levelLimit === 'undefined') {
          return true
        }

        // 在树型中查找对应的区域节点，如果不存在则禁止添加子节点区域
        const zoneNode = this.tableTree?.getNodeByKey(zone.rid)
        if (!zoneNode) {
          return true
        }
        // 计算该节点下子节点数量是否超出限制
        const zoneNodeChildren = zoneNode.getChildren()
        if (!zoneNodeChildren) {
          return false
        }
        return zoneNodeChildren.length >= levelLimit
      },
      areaNameLabel() {
        if (this.isZoneConfigNode) {
          return this.$t('writeFreq.configName')
        }
        return this.$t('dialog.areaName')
      },
      maxZoneNo() {
        const zoneCache = this.getHasSettingZone(this.oneChannelZone)
        if (!zoneCache || !zoneCache.setting.model) {
          return 1
        }

        // 当前区域序号最大值为上级的限制值
        const parentLevel = this.oneChannelZone.zoneLevel - 1
        switch (zoneCache.setting.model) {
          case deviceModels.TD910PSDCModel: // BF-TD910P(SDC)
          case deviceModels.TD910SDCModel: // BF-TD910(SDC)
          case deviceModels.TD920Model: // BF-TD920
            const DP109SDCLimits = getModelZoneLevelLimit(zoneCache.setting.model)
            return DP109SDCLimits[parentLevel] || 4
          // 一级区域默认有32个
          // case '511SCD00':
          // case 'TD081100':
          // case '825SDC00':
          // case '8250FR00':
          // case '930SDC00':
          // case '930SVT00':
          default:
            const limits = getModelZoneLevelLimit(zoneCache.setting.model)
            return limits[parentLevel] || 32
        }
      },
      zoneConfigModelList() {
        return zoneModels
          .map(model => {
            return {
              label: getDeviceModelName(model) || getModelName(model),
              value: model,
            }
          })
          .sort((a, b) => a.label.localeCompare(b.label))
      },
      channelZoneModel() {
        if (!this.isZoneConfigNode) {
          return ''
        }
        const zoneCache = this.getHasSettingZone(this.oneChannelZone)
        if (!zoneCache) {
          return ''
        }
        return zoneCache.setting.model
      },
      zoneTreeOption() {
        return {
          selectMode: 1,
          select: this.zoneTreeSelect,
          click: this.clickNode,
        }
      },
      isZoneConfigNode() {
        return this.oneChannelZone.zoneParent === bfutil.DefOrgRid
      },
      showZoneEditor() {
        return this.channelZones.filter(item => item.rid !== bfutil.DefOrgRid).length > 0
      },
      zoneSelectedKeys() {
        return this.selectZoneNode ? [this.selectZoneNode.key] : []
      },
      batchTargetTotalLen() {
        return this.batchTreeTarget.length
      },
      batchSetTree() {
        return this.$refs[this.batchTreeId]
      },
      tableTree() {
        return this.$refs[this.zoneTreeId]
      },
      batchTreeOption() {
        return {
          selectMode: 3,
          select: this.batchSetTreeOnSelect,
          click: this.clickNode,
        }
      },
      batchSettingStatus() {
        // 判断所有信道数据是否更新成功，失败则显示“exception”状态
        if (this.batchSettingFailed) {
          return 'exception'
        }

        if (this.batchSettingPercentage === 100) {
          // 全部信道更新成功
          return 'success'
        }

        // 信道更新时默认状态
        return undefined
      },
      deviceName() {
        return this.device.selfId || this.device.dmrId || ''
      },
      channelDialogTitle() {
        const name = this.deviceName
        if (!name) return this.$t('dialog.channelManager')
        return `${this.$t('dialog.channelManager')} - ${name}`
      },
      transferTitles() {
        return [this.$t('dialog.unListenGroup'), this.$t('dialog.listenGroup')]
      },
      buttonTexts() {
        return ['', '']
      },
      transferLabelFormat() {
        return {
          noChecked: '${total}',

          hasChecked: '${checked}/${total}',
        }
      },
      selectedChannelZoneList() {
        const getZoneChildren = data => {
          const children = []
          if (!data) {
            return children
          }

          try {
            for (let i = 0; i < this.channelZones.length; i++) {
              const item = this.channelZones[i]
              if (data.rid < 32) {
                const setting = JSON.parse(item.setting)
                if (data.rid.startsWith(setting.title) && data.rid.endsWith(setting.model)) {
                  children.push(item)
                }
              } else if (item.zoneParent === data.rid) {
                children.push(item)
              }
            }
          } catch (e) {
            bfglob.console.warn('selectedChannelZoneList getZoneChildren catch:', e)
          }

          return children
        }
        const getChildren = data => {
          return getZoneChildren(data).map(item => {
            return createNodeData(item)
          })
        }
        const createNodeData = data => {
          const res = {
            value: data.rid,
            label: data.zoneLevel < 2 ? `${data.zoneTitle}` : `${data.zoneNo} - ${data.zoneTitle}`,
          }
          const children = getChildren(data)
          if (children.length > 0) {
            res.children = children
          }

          return res
        }

        return this.channelZones
          .filter(data => {
            return data.zoneParent === bfutil.DefOrgRid
          })
          .map(data => {
            const res = createNodeData(data)
            res.disabled = !res.children || res.children.length === 0
            return res
          })
      },
      // 从其他设备读取信道设置联级选择框数据源
      cascaderDevOpts() {
        const opts = []
        // 读取第二级设备的信道数据
        const getChannelList = device => {
          const list = []

          // 解析终端设备的信道数据
          let channels = []
          try {
            const channel = device.channel || '{"channels": []}'
            channels = JSON.parse(channel).channels ?? []
          } catch (_e) {
            // no-empty
          }

          // 读取设备的信道配置数据
          for (let i = 0; i < channels.length; i++) {
            const item = channels[i]
            const channelNo = item.no
            list.push({
              value: channelNo,
              label: `${this.$t('msgbox.channel')}-${channelNo}`,
            })
          }

          return list.sort((a, b) => {
            return bfutil.sortByProps(a, b, { value: 'asc' })
          })
        }

        for (const k in this.gdevices) {
          const device = this.gdevices[k]
          // 跳过不支持信道设置的终端设备
          if (isExcludeType(device.deviceType)) continue

          // 跳过没有信道配置的
          const children = getChannelList(device)
          if (children.length === 0) continue

          opts.push({
            value: device.rid,
            label: device.selfId,
            children: children,
            // disabled: children.length === 0,
          })
        }

        return opts
      },
      // deviceList() {
      //   // 过滤那些不能设置信道的类型终端
      //   return Object.keys(this.globalDeviceList)
      //     .filter(key => !isExcludeType(this.globalDeviceList[key].deviceType))
      //     .map(key => this.globalDeviceList[key])
      // },
      sendGroupList() {
        return [
          {
            label: this.$t('dialog.fullCall'),
            key: bfglob.fullCallDmrId,
          },
        ].concat(this.willListenData)
      },
      channelVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      locale() {
        return this.$i18n.locale
      },
      isCN() {
        return this.locale === SupportedLang.zhCN
      },
      isEN() {
        return this.locale === SupportedLang.enUS
      },
      isEn() {
        return this.isEN
      },
      isFR() {
        return this.locale === SupportedLang.fr
      },
      channelManagerLabelWidth() {
        return this.isFR || this.isEN ? '130px' : '100px'
      },
      labelWidth() {
        return this.isFR || this.isEN ? '140px' : '100px'
      },
      newZoneConfigLabelWidth() {
        return this.isFR ? '140px' : this.isEN ? '120px' : '100px'
      },
    },
    watch: {
      channelData: {
        deep: true,
        handler(data) {
          // 缓存当前信道配置到channelsMap中
          if (this.checkChannelDataIsValid(data)) {
            this.channelsMap.set(data.no, data)
          } else {
            this.channelsMap.delete(data.no)
          }
        },
      },
    },
    beforeMount() {
      // 每次打开信道管理窗口，都重新初始化信道数据
      this.initChannel()
    },
  }
</script>

<style lang="scss">
  .el-popper.cascader-popper {
    .el-cascader-menu__wrap {
      // 28 * 12 = 336px
      height: 336px;
    }

    .el-cascader-node {
      height: 28px;
      line-height: 1;
    }
  }

  .el-dialog.channel-settings-dialog {
    width: 520px;

    .el-dialog__footer {
      border-top: 1px solid #ddd;
    }

    .el-form.channel-page-form .el-form-item {
      margin-bottom: unset;

      &.cascader-wrap .el-form-item__content {
        display: flex;
        flex-wrap: nowrap;
      }

      .cascader-box.el-cascader {
        flex: auto;
        width: 100%;
      }
    }

    .channel-manager-transfer.el-transfer {
      width: 100%;

      .el-transfer-panel {
        width: 100%;

        &:first-child {
          // 默认是278px, 左侧没有footer,需要加上footer的40px高度
          --el-transfer-panel-body-height: 318px;
        }

        .el-transfer-panel__header {
          &,
          .el-checkbox {
            height: 32px;
            line-height: 32px;
          }
        }

        .el-transfer-panel__body {
          min-height: 236px;

          .el-transfer-panel__filter {
            margin: 0;
            padding: 6px 10px;

            .el-input__prefix {
              top: 5px;
              height: 32px;
            }
          }

          .el-transfer-panel__list {
            &.is-filterable {
              height: calc(100% - 44px);
            }

            .el-checkbox.el-transfer-panel__item {
              &,
              .el-checkbox__label {
                height: 24px;
                line-height: 24px;
              }

              .el-checkbox__label {
                padding-left: 20px;
              }

              .el-checkbox__input {
                top: 5px;
              }
            }
          }
        }

        .el-transfer-panel__footer {
          padding: 4px 8px;
        }
      }

      .el-transfer__buttons {
        padding: 0;
        display: flex;
        flex-direction: column;
        gap: 12px;

        .el-button {
          border-radius: 50%;
          padding: 9px;
          margin: 0;

          span:has(+ [class*='el-icon-']),
          [class*='el-icon-'] + span {
            margin: 0;
          }
        }
      }
    }

    .batch-setting-progress {
      width: 100%;
    }
  }

  .el-dialog.batch-setting-channel-dialog {
    width: 360px;

    .el-dialog__body {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 460px;
      padding: 0;

      .copy-source-device,
      .fancytree-grid-wrapper {
        padding: 0 12px;
      }

      .copy-source-device {
        width: 100%;
        line-height: 28px;
      }

      .fancytree-grid-wrapper {
        height: calc(100% - 30px);
      }
    }

    .el-dialog__footer {
      border-top: 1px solid #ddd;
      padding: 10px;
    }
  }

  .el-dialog.zone-config-dialog {
    width: 420px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.8);
    height: auto;

    .el-dialog__body {
      padding: 10px 10px 0;
    }
  }

  .el-dialog.channel-zone-dialog {
    width: 520px;
    height: 60%;

    .fancytree-custom-icon.iconfont {
      margin-top: 0;
      color: #909399;
    }

    .device_channel.zone-no {
      background-color: rgb(102, 177, 255);
      margin-right: 4px;
    }

    .el-dialog__body {
      padding: 0;
      height: calc(60vh - 45px - 71px);
    }

    @media (min-width: 2560px) {
      .el-dialog__body {
        height: calc(40vh - 45px - 71px);
      }
    }

    .el-dialog__footer {
      border-top: 1px solid #dcdfe6;
    }

    .channel-zone-form {
      padding: 10px;
      overflow: auto;
      border-left: 1px solid #dddddd;

      .el-form-item__label {
        padding: 0;
      }
    }

    .channel-zone-container {
      & > * {
        flex-basis: 50%;
      }
    }
  }

  @media (min-width: 2560px) {
    .el-dialog.channel-zone-dialog {
      height: 40vh;
    }
  }
</style>
