import { defineComponent, h, computed, ref } from 'vue'
import { ElTransfer } from 'element-plus'
import 'element-plus/es/components/transfer/style/css'
import './main.scss'

export default defineComponent({
  name: 'BfTransfer',
  props: {
    modelValue: {
      type: Array,
      default: () => [],
    },
    ...ElTransfer.props,
  },
  emits: ['update:modelValue', 'change', 'left-check-change', 'right-check-change'],
  setup(props: InstanceType<typeof ElTransfer>['$props'], { emit, slots, expose, attrs }) {
    const transferVal = computed({
      get() {
        return props.modelValue
      },
      set(val) {
        emit('update:modelValue', val)
      },
    })

    const transferProps = computed(() => {
      return {
        ...attrs,
        ...props,
        class: ['bf-transfer', attrs.class].filter(Boolean).join(' '),
        modelValue: transferVal.value,
        'onUpdate:modelValue': val => {
          transferVal.value = val
        },
      }
    })

    const transferRef = ref<InstanceType<typeof ElTransfer>>()
    expose({
      transferRef,
      clearQuery: which => transferRef.value?.clearQuery(which),
    })

    // 使用 h 函数渲染 ElTransfer
    return () =>
      h(
        ElTransfer,
        {
          ...transferProps.value,
          ref: transferRef,
        },
        slots
      )
  },
})
