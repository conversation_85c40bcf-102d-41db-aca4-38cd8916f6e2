@use '@/css/common.scss';
@use '@/components/bfCheckbox/main.scss';

.el-transfer.bf-transfer {
  @extend .bf-component-size;

  // 基础样式变量
  --el-transfer-border-color: rgba(148, 204, 232, 1);
  --el-transfer-border-radius: 0;
  --el-transfer-panel-header-bg-color: rgba(6, 121, 204, 0.46);
  --el-transfer-pane-border-size: 1px;
  
  .el-transfer-panel {
    border: var(--el-transfer-pane-border-size) solid var(--el-transfer-border-color);
    border-radius: var(--el-transfer-border-radius);
    background-color: transparent;

          .el-checkbox {
        @extend .bf-checkbox;

        .el-checkbox__label {
          color: #fff;
        }
      }

    .el-transfer-panel__header {
      background-color: var(--el-transfer-panel-header-bg-color);
      color: #fff;
      padding: 10px 15px;
      border: none;
    }

    .el-transfer-panel__body {
      border: none;
    }
  }
}